import * as fs from 'fs';
import * as path from 'path';
import { LanceDbIndex } from './lanceDbIndex';

export async function readJsonFile(filePath: string): Promise<any> {
    try {
        const data = await fs.promises.readFile(filePath, "utf8");
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading or parsing JSON file: ${error}`);
        throw error;
    }
}

export async function writeJsonFile(filePath: string, data: any) {
    try {
        await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
        console.error(`Error writing file ${filePath}:`, error);
        throw error;
    }
}

function getFilesInDirectory(dirPath: string): string[] {
    try {
        const items = fs.readdirSync(dirPath);
        const files = items.filter(item => {
            const fullPath = path.join(dirPath, item);
            return fs.statSync(fullPath).isFile();
        }).map(item => path.join(dirPath, item));
        return files;
    } catch (error) {
        console.error(`Error reading directory: ${dirPath}`, error);
        return [];
    }
}

const repos = [
    "odsp-go",
    "virtual-elastic",
    "mmvsops",
    "BDR",
    "VNFP",
    "ST",
    "VPP_vFW",
    "Hadoop-Yarn"
];

interface RepoInfo {
    repoDir: string;
    testDir: string;
}

const repoInfos: Record<string, RepoInfo> = {
    "VPP_vFW": {
        "repoDir": "/home/<USER>/workspace/thread_1/vCube/VPP/VPP_vFW",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/vcube/VPP_vFW"
    },
    "VNFP": {
        "repoDir": "/home/<USER>/workspace/thread_1/vCube/VNFP",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/vcube/VNFP"
    },
    "mmvsops": {
        "repoDir": "/home/<USER>/workspace/thread_1/ivamp/zxoms/mmvsops",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/cdn/mmvsops"
    },
    "ST": {
        "repoDir": "/home/<USER>/workspace/thread_1/ZXCDN/CACHE/ST",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/cdn/ST"
    },
    "BDR": {
        "repoDir": "/home/<USER>/workspace/thread_1/DAP/MANAGER/BDR",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/daip/BDR"
    },
    "Hadoop-Yarn": {
        "repoDir": "/home/<USER>/workspace/thread_1/DAP/ZDH/Hadoop-Yarn",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/daip/Hadoop-Yarn"
    },
    "odsp-go": {
        "repoDir": "/home/<USER>/workspace/thread_1/OES/OES_Devops/odsp-go",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/idn/odsp-go"
    },
    "virtual-elastic": {
        "repoDir": "/home/<USER>/workspace/thread_1/OES/OES_Devops/virtual-elastic",
        "testDir": "/home/<USER>/zyf/AI/zero_staff/code3/repo_understander_evaluation/case/idn/virtual-elastic"
    }
};

async function indexing() {
    for (let i=0;i<repos.length;i++) {
        console.log("==================");
        const repoName = repos[i];
        console.log("start indexing:", repoName);
        const lanceDb = new LanceDbIndex();
        const repoInfo = repoInfos[repoName];
        const repoDir = repoInfo["repoDir"];
        await lanceDb.indexing(repoDir);
        await runTest(lanceDb, repoName);
    }
}

async function runTest(lanceDb: LanceDbIndex, repoName: string) {
    console.log("evaluation:", repoName);
    const repoInfo = repoInfos[repoName];
    const repoDir = repoInfo["repoDir"];
    const testDir = repoInfo["testDir"];
    const caseFiles = getFilesInDirectory(testDir);
    for (let i=0;i<caseFiles.length;i++) {
        const data = await readJsonFile(caseFiles[i]);
        data["retrieve_files"] = [];
        const task: string = data["task"];
        const retrieveFiles = await lanceDb.retrieve(repoDir, task);
        data["retrieve_files"] = retrieveFiles;
        await writeJsonFile(caseFiles[i], data);
    }
}
/*
对代码库进行索引，并在测试集上验证，测试结果会写入测试文件中。
若要重新索引某个代码库，到~/.zeroAgent/index/lancedb目录下，将该代码库对应的.lance文件删除。
*/
//indexing();