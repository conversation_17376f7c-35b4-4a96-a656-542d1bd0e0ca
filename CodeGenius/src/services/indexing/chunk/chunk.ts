import { getUriPathBasename } from "../util/uri.js";

export interface ChunkWithoutID {
  filepath: string;
  content: string;
  startLine: number;
  endLine: number;
  signature?: string;
  otherMetadata?: { [key: string]: any };
}

export interface Chunk extends ChunkWithoutID {
  digest: string;
  index: number; // Index of the chunk in the document at filepath
}

export function shouldChunk(fileUri: string, contents: string): boolean {
  if (contents.length > 1000000) {
    // if a file has more than 1m characters then skip it
    return false;
  }
  if (contents.length === 0) {
    return false;
  }
  const baseName = getUriPathBasename(fileUri);
  return baseName.includes(".");
}

async function genToArr<T>(generator: AsyncGenerator<T>): Promise<T[]> {
  const result: T[] = [];
  for await (const item of generator) {
    result.push(item);
  }
  return result;
}

export async function genToStrs(
  generator: AsyncGenerator<ChunkWithoutID>,
): Promise<ChunkWithoutID[]> {
  const chunks = await genToArr(generator);
  return chunks;
}

export async function countLinesAsync(content: string): Promise<number> {
  const lines = content.split('\n');
  const lineCount = lines.length;
  return lineCount;
}
