import { Anthropic } from "@anthropic-ai/sdk"
import * as fs from "fs"
import OpenAI from "openai"
import { convertToOpenAiMessages } from "../api/transform/openai-format";

export async function printPromptZte(systemPrompt: string, messages: Anthropic.Messages.MessageParam[], workDir: string, taskId: string, lastTs: number) {
	const finalPrompt = messages.length === 1 ? systemPrompt : '###systemPrompt###'

	const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
		{ role: "system", content: systemPrompt },
		...convertToOpenAiMessages(messages),
	]

	const fileName = obtainFilePath(workDir, taskId, lastTs);

	const formattedMessages = JSON.stringify(openAiMessages, null, 2);
	fs.writeFileSync(fileName, formattedMessages);
}

function obtainFilePath(workDir: string, taskId: string, lastTs: number): string {
	const date = new Date(lastTs)

	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');

	const hours = String(date.getHours()).padStart(2, '0');
	const minutes = String(date.getMinutes()).padStart(2, '0');
	const seconds = String(date.getSeconds()).padStart(2, '0');

	fs.mkdirSync(`${workDir}/prompts/${year}${month}${day}/${taskId}`, { recursive: true })

	return `${workDir}/prompts/${year}${month}${day}/${taskId}/${hours}${minutes}${seconds}.json`;
}