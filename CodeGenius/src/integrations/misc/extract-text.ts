import * as path from "path"
// @ts-ignore-next-line
import pdf from "pdf-parse/lib/pdf-parse"
import mammoth from "mammoth"
import fs from "fs/promises"
import { isBinaryFile } from "isbinaryfile"
import * as chardet from "jschardet"
import * as iconv from "iconv-lite"
import { parseFile } from "../../services/tree-sitter"
import { loadRequiredLanguageParsers } from "../../services/tree-sitter/languageParser"
import { ApiHandler } from '../../api';
import { OpenAiHandler } from '../../api/providers/openai';

export async function detectEncoding(fileBuffer: Buffer, fileExtension?: string): Promise<string> {
    const detected = chardet.detect(fileBuffer)
    if (typeof detected === "string") {
        return detected
    } else if (detected && (detected as any).encoding) {
        return (detected as any).encoding
    } else {
        if (fileExtension) {
            const isBinary = await isBinaryFile(fileBuffer).catch(() => false)
            if (isBinary) {
                throw new Error(`Cannot read text for file type: ${fileExtension}`)
            }
        }
        return "utf8"
    }
}

export async function extractTextFromFile(filePath: string, api: ApiHandler): Promise<string> {
    try {
        await fs.access(filePath)
    } catch (error) {
        throw new Error(`File not found: ${filePath}`)
    }
    const fileExtension = path.extname(filePath).toLowerCase()
    let content: string = ""
    switch (fileExtension) {
        case ".pdf":
            content = await extractTextFromPDF(filePath)
            break
        case ".docx":
            content = await extractTextFromDOCX(filePath)
            break
        case ".ipynb":
            content = await extractTextFromIPYNB(filePath)
            break
        default:
            const isBinary = await isBinaryFile(filePath).catch(() => false)
            if (!isBinary) {
                content = await fs.readFile(filePath, "utf8")
            } else {
                throw new Error(`Cannot read text for file type: ${fileExtension}`)
            }
    }
    return maxTokenProcess(content, filePath, api)
}

async function extractTextFromPDF(filePath: string): Promise<string> {
    const dataBuffer = await fs.readFile(filePath)
    const data = await pdf(dataBuffer)
    return data.text
}

async function extractTextFromDOCX(filePath: string): Promise<string> {
    const result = await mammoth.extractRawText({ path: filePath })
    return result.value
}

async function extractTextFromIPYNB(filePath: string): Promise<string> {
    const fileBuffer = await fs.readFile(filePath)
    const encoding = await detectEncoding(fileBuffer)
    const data = iconv.decode(fileBuffer, encoding)
    const notebook = JSON.parse(data)
    let extractedText = ""

    for (const cell of notebook.cells) {
        if ((cell.cell_type === "markdown" || cell.cell_type === "code") && cell.source) {
            extractedText += cell.source.join("\n") + "\n"
        }
    }

    return extractedText
}

async function maxTokenProcess(content: string, filePath: string, api: ApiHandler): Promise<string> {
    const newlineRegex = /\r\n|\r|\n/g;
    const newlineCount = (content.match(newlineRegex) || []).length;
    let maxLines = await getMaxLines(api);
    if (newlineCount < maxLines) {
        return content;
    }
    const languageParsers = await loadRequiredLanguageParsers([filePath]);
    let definition = await parseFile(filePath, languageParsers);
    return definition ? definition : "";
}

export async function getMaxLines(api: ApiHandler): Promise<number> {
    let contextWindow = api.getModel().info.contextWindow || 128_000;
    if (api instanceof OpenAiHandler && api.getModel().id.toLowerCase().includes("deepseek")) {
        contextWindow = 64_000
    }
    let maxtokens = contextWindow - 10_000;
    let maxLines = maxtokens / 20;
    return maxLines;
}

export function addLineNumbers(content: string, startLine: number = 1): string {
    // If content is empty, return empty string - empty files should not have line numbers
    // If content is empty but startLine > 1, return "startLine | " because we know the file is not empty
    // but the content is empty at that line offset
    if (content === "") {
        return startLine === 1 ? "" : `${startLine} | \n`
    }

    // Split into lines and handle trailing newlines
    const lines = content.split("\n")
    const lastLineEmpty = lines[lines.length - 1] === ""
    if (lastLineEmpty) {
        lines.pop()
    }

    const maxLineNumberWidth = String(startLine + lines.length - 1).length
    const numberedContent = lines
        .map((line, index) => {
            const lineNumber = String(startLine + index).padStart(maxLineNumberWidth, " ")
            return `${lineNumber} | ${line}`
        })
        .join("\n")

    return numberedContent + "\n"
}

// Checks if every line in the content has line numbers prefixed (e.g., "1 | content" or "123 | content")
// Line numbers must be followed by a single pipe character (not double pipes)
export function everyLineHasLineNumbers(content: string): boolean {
    const lines = content.split(/\r?\n/) // Handles both CRLF (carriage return (\r) + line feed (\n)) and LF (line feed (\n)) line endings
    return lines.length > 0 && lines.every((line) => /^\s*\d+\s+\|(?!\|)/.test(line))
}

/**
 * Strips line numbers from content while preserving the actual content.
 *
 * @param content The content to process
 * @param aggressive When false (default): Only strips lines with clear number patterns like "123 | content"
 *                   When true: Uses a more lenient pattern that also matches lines with just a pipe character,
 *                   which can be useful when LLMs don't perfectly format the line numbers in diffs
 * @returns The content with line numbers removed
 */
export function stripLineNumbers(content: string, aggressive: boolean = false): string {
    // Split into lines to handle each line individually
    const lines = content.split(/\r?\n/)

    // Process each line
    const processedLines = lines.map((line) => {
        // Match line number pattern and capture everything after the pipe
        const match = aggressive ? line.match(/^\s*(?:\d+\s)?\|\s(.*)$/) : line.match(/^\s*\d+\s+\|(?!\|)\s?(.*)$/)
        return match ? match[1] : line
    })

    // Join back with original line endings (carriage return (\r) + line feed (\n) or just line feed (\n))
    const lineEnding = content.includes("\r\n") ? "\r\n" : "\n"
    return processedLines.join(lineEnding)
}