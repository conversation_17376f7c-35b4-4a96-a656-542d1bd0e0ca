import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI, { AzureOpenAI } from "openai"

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../index"
import { convertToOpenAiMessages } from "../transform/openai-format"
import { ApiStream } from "../transform/stream"
import { SYSTEM_FOR_TOOL_SELECT } from "@/core/prompts/systemForSelectTool"
import { SYSTEM_PROMPT_FOR_USE_TOOL } from "@/core/prompts/systemWithOneToolDetail"
import { cwd } from "@/core/task"
import { ApiConfiguration, ApiHandlerOptions, ModelInfo, nebulaModelInfo, nebulaReasonModelInfo, qwenCoderModelInfo } from "@/shared/api"
import { ZeroLogger } from "@/utils/logger"
import { McpHub } from "@/services/mcp/McpHub"


const logger = new ZeroLogger("SelectToolHandler");

export class SelectToolHandler {
	private options: ApiHandlerOptions
	private client: OpenAI
	private model: { id: string; info: ModelInfo } = {
		id: "/model",
		info: nebulaModelInfo,
	}

	constructor(configuration: ApiConfiguration) {
		const { apiProvider, ...options } = configuration
		this.options = options

		switch (apiProvider) {
			case "openai":
				this.client = new OpenAI({
					baseURL: this.options.openAiBaseUrl,
					apiKey: this.options.openAiApiKey,
				})
				break;
			case "deepseek":
				this.client = new OpenAI({
					baseURL: this.options.nebulaModelApiUrl,
					apiKey: this.options.nebulaModelApiKey,
				})
				break;
			case "qwen-native":
				this.client = new OpenAI({
					baseURL: this.options.qwenModelApiUrl,
					apiKey: this.options.qwenModelApiKey,
				})

				this.model = {
					id: this.options.qwenModelId || "/model",
					info: qwenCoderModelInfo,
				}

				break;
			case "nebula":
				this.client = new OpenAI({
					baseURL: this.options.nebulaModelApiUrl,
					apiKey: this.options.nebulaModelApiKey,
				})

				this.model = {
					id: this.options.nebulaModelId || "/model",
					info: nebulaModelInfo,
				}
				break;
			case "nebula-reason":
				this.client = new OpenAI({
					baseURL: this.options.nebulaReasonModelApiUrl,
					apiKey: this.options.nebulaReasonModelApiKey,
				})

				this.model = {
					id: this.options.nebulaReasonModelId || "/model",
					info: nebulaReasonModelInfo,
				}
				break;
			default:
				this.client = new OpenAI({
					baseURL: this.options.nebulaModelApiUrl,
					apiKey: this.options.nebulaModelApiKey,
				})
		}
	}

	async queryToolPrompt(messages: Anthropic.Messages.MessageParam[], isPlanMode: boolean, mcpHub: McpHub,): Promise<string> {
		const selectToolPrompt = await SYSTEM_FOR_TOOL_SELECT(isPlanMode, mcpHub)

		const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
			{ role: "system", content: selectToolPrompt },
			...convertToOpenAiMessages(messages),
		]

		const response = await this.client.chat.completions.create({
			model: this.model.id,
			messages: openAiMessages,
			temperature: 0,
			stream: false,
		});

		let attempts = 0;
		let content = response.choices[0]?.message?.content || "";
		let toolName = this.getToolNameFromAnswer(content);

		while (attempts < 3 && !toolName) {
			attempts++;
			logger.warn(`Attempt ${attempts}: Tool name not found, retrying...`);

			const retryResponse = await this.client.chat.completions.create({
				model: this.model.id,
				messages: openAiMessages,
				temperature: 0,
				stream: false,
			});

			content = retryResponse.choices[0]?.message?.content || "";
			toolName = this.getToolNameFromAnswer(content);
		}

		if (!toolName) {
			logger.error("Failed to retrieve tool name after 3 attempts");
			throw new Error("Tool name extraction failed");
		}

		const prompt = await SYSTEM_PROMPT_FOR_USE_TOOL(toolName, cwd, mcpHub)

		return prompt;
	}

	private getToolNameFromAnswer(answer: string): string {
		let toolName = this.extractContent(answer, '*-*')

		if (!toolName) {
			logger.warn(`Failed to extract tool name! answer is , ${answer})`)

			const list: string[] = [
				"replace_in_file",
				"write_to_file",
				"execute_command",
				"read_file",
				"search_files",
				"list_files",
				"list_code_definition_names",
				"use_mcp_tool",
				"access_mcp_resource",
				"ask_followup_question",
				"read_code_item",
				"attempt_completion",
				"new_task",
				"plan_mode_respond",
				"load_mcp_documentation"
			];

			for (const [index, item] of list.entries()) {
				if (answer.includes(`<${item}>`) && answer.includes(`</${item}>`)) {
					toolName = item;

					logger.warn(`abstract tool: ${toolName}`)
					break;
				}
			}
		}
		return toolName
	}

	private extractContent(str: string, delimiter: string): string {
		const parts = str.split(delimiter);
		return parts.length >= 3 ? parts[1] : '';
	}
}
