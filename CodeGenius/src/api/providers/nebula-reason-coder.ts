import { Anthropic } from "@anthropic-ai/sdk"
import OpenAI, { AzureOpenAI } from "openai"
import { ApiHandlerOptions, ModelInfo, nebulaReasonModelInfo } from "../../shared/api"
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../index"
import { convertToOpenAiMessages } from "../transform/openai-format"
import { ApiStream } from "../transform/stream"

export class NebulaReasonHandler implements ApiHandler {
	private options: ApiHandlerOptions
	private client: OpenAI

	constructor(options: ApiHandlerOptions) {
		this.options = options
		this.client = new OpenAI({
			baseURL: this.options.nebulaReasonModelApiUrl,
			apiKey: this.options.nebulaReasonModelApiKey,
		})
	}

	async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
		const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
			{ role: "system", content: systemPrompt },
			...convertToOpenAiMessages(messages),
		]
		const stream = await this.client.chat.completions.create({
			model: this.getModel().id,
			messages: openAiMessages,
			temperature: 0,
			stream: true,
			stream_options: { include_usage: true },
		})
		let hasReasoning: boolean = false
		for await (const chunk of stream) {
			const delta = chunk.choices[0]?.delta

			if(delta?.content && delta?.content==='<think>\n'){
				hasReasoning = true
				continue
			}

			if(delta?.content && delta?.content.includes('</think>')){
				hasReasoning = false
				continue
			}
			
			if (delta?.content && hasReasoning) {
				yield {
					type: "reasoning",
					reasoning: delta.content,
				}
			}

			if (!hasReasoning && delta?.content) {
				yield {
					type: "text",
					text: delta.content,
				}
			}
			if (chunk.usage) {
				yield {
					type: "usage",
					inputTokens: chunk.usage.prompt_tokens || 0,
					outputTokens: chunk.usage.completion_tokens || 0,
				}
			}
		}
	}

	getModel(): { id: string; info: ModelInfo } {
		return {
			id: this.options.nebulaReasonModelId || "/model",
			info: nebulaReasonModelInfo,
		}
	}
}
