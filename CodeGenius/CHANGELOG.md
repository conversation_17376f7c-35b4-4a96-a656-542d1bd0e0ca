# Change Log
## [0.5.0]
- 增加对MCP服务的支持
- 增加@file时对文件路径、文件名带空格的支持
- 增加代码骨架文档模版的快速生成
- 星云的默认模型改为6.0的快速反应模型和思路链模型
- 解决plan/act切换有时会导致设置的模型错乱问题
- 修复代码库带.git导致AI代码生成率统计不到问题
- 修复文件非UTF-8编码格式导致的文件写入乱码问题
## [0.4.0]
- 增加智能上下文管理，保证已读取文件是最新内容，并减小提示词大小
- replace工具优化，增加修复与校验，减小出错概率
- 优化plan、act模式使用专有提示词，消除相互干扰
- 增加mermaid图的保存、和图形代码的复制功能
- 提供Ctrl+’实现快捷添加选中内容到零号员工
- 增加OpenAI Compatible模型配置设置自定义上下文窗口大小功能
- 增加零号员工插件的重新加载按钮
- 修复1类灰屏问题，减小了灰屏概率 
- 解决零号员工窗口拖动后无法用@方式选择文件问题
## [0.3.2]
- 优化mention文件选不中的问题
- 优化失焦的问题
- 解决文件搜寻的问题

## [0.3.1]
- 增加灰屏异常捕获并打印错误信息
- 使用体验优化

## [0.3.0]
- 零号员工支持设置排除的目录和文件
- 优化输入目录选不中的问题
- 支持checkpoints能力，支持恢复文件至某次对话前
- 增加过程指标统计及上报
- 支持自动获取终端工具
- 支持右键一键添加代码及终端内容至零号员工对话框
- 支持小灯泡内嵌使用零号员工修复选项

## [0.2.0]
- 增加星云v6.0推理模型；
- 增加读取使用代码库自定义通用信息、要求；
- 修复plan、act模式切换model使用等问题；

## [0.1.2]
- Mention文件和目录的数量不限制
- 优化代码以及采集支持数据飞轮

## [0.1.1]
- 优化执行任务时文件加载速度

## [0.1.0]
- 增加plan/act模式
- 增加控制台打印workspace目录

## [0.0.9]
- 提高Mention文件数量限制从2000到5000
- 增加Mention排除目录[target,classes]

## [0.0.8]
- 默认配置排除代理'10.55.32.26', 'zdsp.zx.zte.com.cn'
- 增加访问统计

## [0.0.7]
- 增加星云模型

## [0.0.6]
- 优化适配deepseek-R1模型

## [0.0.5]
- 国际化改造
- 增加通义千问模型
- 优化界面呈现

## [0.0.4]
- 初始版本发布
