export default {
  "welcomeTitle": "我能为你做什么？",
  "welcomeDesc": {
    "0": "得益于",
    "1": "我可以一步一步地处理复杂的软件开发任务。借助可让我创建和编辑文件、探索复杂项目、使用浏览器并执行终端命令（授予权限后）的工具，我可以帮助您超越代码完成或技术支持的范畴。我甚至可以使用MCP来创建新工具并扩展我自己的能力。"
  },
  "welcome": {
    "title": "您好，我是零号员工",
    "describe": {
      "0": "基于",
      "1": "零号员工提供的编码和访问工具的能力，",
      "2": "我可以一步一步地处理复杂的软件开发任务。可以创建和编辑文件、探索复杂项目、使用浏览器和执行终端命令的工具。还可以使用MCP来创建新工具并扩展我自己的能力。"
    },
    "require": "零号员工需要大模型的API提供能力。",
    "begin": "开始向我提问吧",
  },
  "setting": {
    "name": "设置",
    "save": "保存",
    "tab_plan_mode": "计划模式",
    "tab_act_mode": "执行模式",
    "api_provider": {
      "name": "已支持API",
      "note": "Note: 零号员工使用了复杂提示词，模型能力越强，其生成效果越好",
      "deepseek": {
        "name": "DeepSeek",
        "api_key": "DeepSeek API 秘钥",
        "describe": "此密钥存储在本地，仅用于从此扩展发出 API 请求。",
        "describe_1": "您可以通过在此处注册获取 DeepSeek API 密钥。",
      },
      "openai": {
        "ask_version": "设置Azure API版本号",
        "complex_model_tip": "零号员工使用了复杂、强大的提示词，使用Claude系列的模型效果会更好，使用其它小模型可能达不到预期效果。",
      },
      "qwen_coder": "千问模型",
      "nebula": "星云v6.0-快速反馈",
      "nebula-reason": "星云v6.0-思维链",
      "llama_coder": "Llama-Coder",
      "api_key_placeholder": "请输入API秘钥...",
      "api_url_placeholder": "请输入模型url...",
      "api_model_id_placeholder": "请输入模型ID...",
    },
    "model": {
      "name": "模型",
      "model_id": "模型ID",
      "model_url": "模型URL",
      "model_key": "模型Key",
      "select": "选择一个模型...",
      "info": {
        "image": {
          "support": "支持图片",
          "not_support": "不支持图片",
        },
        "browser": {
          "support": "支持使用浏览器",
          "not_support": "不支持浏览器",
        },
        "computer": {
          "support": "支持使用计算机",
          "not_support": "不支持使用计算机",
        },
        "prompt": {
          "support": "支持提示词缓存",
          "not_support": "不支持提示词缓存",
        },
      },
      "capacity": {
        "max_tokens": "最大输出",
        "input_price": "输入价格",
        "cache_writes_price": "缓存写入价格",
        "cache_reads_price": "缓存读取价格",
        "output_price": "输出价格",
        "million": "百万",
      },
      "configuration": {
        "configuration": "模型配置",
        "support_image": "支持图片",
        "context_window_size": "上下文窗口大小",
        "max_output_tokens": "最大输出token数",
        "input_price": "输入价格 / 每百万Token",
        "output_price": "输出价格 / 每百万Token",
        "enable_r1_format": "开启R1消息格式化",
        "temperature": "温度"
      },
      "header":{
        "custom_header": "自定义请求头",
        "add_header": "添加请求头",
        "remove": "删除"
      }
    },
    "view": {
      "done": "完成",
      "custom_instructions": "自定义指令",
      "describe": "这些指令会被添加到每个请求的提示词最后面",
      "placeholder": "例如'在最后运行单元测试'、'使用带有 async/await 的 TypeScript'、'使用中文回答'",
      "use_different_model": "在计划模式（Plan）和执行模式（Act）中使用不同的模型",
      "use_different_model_desc": "在计划模式（Plan）和执行模式（Act）之间切换时，系统会保留前一模式中使用的API和模型。例如，当使用强大的推理模型设计架构后，切换至更经济的编码模型来执行该计划时，这一功能将非常有用。",
      "dev": {
        "reset_tate": "重置状态",
        "describe": "这将重置扩展中的所有全局状态和秘密存储。",
      },
      "feedback": {
        "describe": "如果你有任何问题或反馈，请点击",
        "link": "反馈地址",
      },
    },
    "mcp": {
      "done": "完成",
      "marketplace": {
        "title": "服务市场",
        "retry": "重试",
        "searching": "搜索MCP...",
        "most_installs": "最多安装",
        "github_stars": "GitHub星数",
        "name": "名称",
        "no_match_found": "未找到匹配的MCP服务器",
        "not_in_market": "未在Marketplace中找到MCP服务器",
        "require_api_key": "需要API密钥",
        "submit_card_desc_prefix": "为帮助其他人发现优秀的MCP服务器，请提交问题至"
      },
      "remote_servers": {
        "title": "远程服务器",
        "desc_prefix": "通过提供名称及其URL添加远程MCP服务器。更多信息请访问此",
        "desc_suffix": "站点",
        "server_name": "服务器名",
        "server_url": "服务器URL",
        "connecting_server": "正在连接到服务器...这可能需要几秒钟。",
        "adding": "正在添加...",
        "add_server": {
          "title": "添加服务器",
          "desc_prefix1": "添加本地MCP服务器的配置文件名为：",
          "desc_prefix2": "，请将服务器名称、命令、参数以及任何所需的环境变量填写在该",
          "desc_suffix": "配置文件",
          "open_prefix": "打开",
        },
        "edit_config": "编辑配置",
        "error": {
          "name_required": "服务器名为必填项",
          "url_required": "服务器URL为必填项",
          "invalid_url": "URL格式无效",
        }
      },
      "installed": {
        "title": "已安装",
        "desc_prefix1": "",
        "desc_prefix2": "MCP协议",
        "desc_prefix3": "启用本地MCP服务器进行通信，这些服务器提供额外的工具和资源来扩展零号员工的功能。您可以使用",
        "desc_prefix4": "社区自制服务器",
        "desc_prefix5": "或要求零号员工创建适合您工作流程的新工具（例如：\"添加一个获取最新npm文档的工具\"）。",
        "desc_suffix": "点击此处查看演示",
        "config_server": "配置MCP服务器",
        "advanced_settings": "高级MCP设置",
        "no_desc": "无描述",
        "unknown": "未知",
        "parameters": "参数",
        "auto_approve": "自动批准",
        "retrying": "正在重试...",
        "retry_connect": "重试连接",
        "deleting": "正在删除...",
        "delete_server": "删除服务器",
        "restarting": "正在重启...",
        "restart_server": "重启服务器",
        "auto_approve_all": "自动批准所有工具",
        "no_tools_found": "未找到工具",
        "no_res_found": "未找到资源"
      }
    },
    "indexingSettingsSection": {
      "codebaseIndex": "代码库索引",
      "codebaseIndexDesc": "代码库库索引后保存在本地",
      "indexingProgressTitle": {
        "loading": "初始化",
        "indexing": "索引中",
        "done": "索引完成"
      }
    }
  },
  "history": {
    "recent_tasks": "最近任务",
    "view_all_history": "查看所有历史",
    "delete_all_history": "删除所有历史",
    "tokens": "令牌数:",
    "cache": "缓存:",
    "api_cost": "API 费用: ¥",
    "history": "历史记录",
    "done": "完成",
    "fuzzy_search": "模糊搜索历史...",
    "clear_search": "清除搜索",
    "newest": "最新",
    "oldest": "最早",
    "most_expensive": "最贵",
    "most_tokens": "最多令牌",
    "most_relevant": "最相关",
    "export": "导出",
    "start_task": "开始一个任务以在此处查看"
  },
  "common": {
    "button_compare": "比较",
    "button_restore": "还原",
    "restore_both": "还原任务和工作区",
    "restore_both_desc": "将任务和项目文件还原到此时点的快照状态",
    "restore_task": "仅还原任务",
    "restore_task_desc": "删除此时点之后的消息（不影响工作区）",
    "restore_workspace": "仅还原工作区",
    "restore_workspace_desc": "将项目文件还原到此时点的快照状态（可能导致任务不同步）",
    "user_edits": "用户编辑",
    "console_logs": "控制台日志",
    "restore_files": "还原文件",
    "restore_files_desc": `将项目文件还原为此时的快照（使用“比较”查看将还原的内容）`,
    "restore_task_only": "仅恢复任务",
    "restore_task_only_desc": "在此点之后删除消息（不影响工作区文件）",
    "restore_file_&_task": "恢复文件和任务",
    "restore_file_&_task_desc": "还原项目的文件并删除超过此点的所有消息"
  },
  "chat": {
    "input_task_placeholder": "输入用户任务（@添加上下文）...",
    "input_message_placeholder": "输入用户信息（@添加上下文）...",
    "auto_approve_settings": {
      "auto_approve": "自动驾驶：",
      "auto_approve_description": "自动驾驶允许零号员工在不请求许可的情况下执行以下操作。请谨慎使用并仅在您了解风险后启用。",
      "read_file": {
        "label": "读取项目文件",
        "short_name": "读取",
        "description": "允许您读取工作区文件。"
      },
      "read_file_external": {
        "label": "读取所有文件",
        "short_name": "读取（所有）",
        "description": "允许您读取计算机上的任何文件。"
      },
      "edit_file": {
        "label": "修改项目文件",
        "short_name": "修改",
        "description": "允许修改工作区文件。"
      },
      "edit_file_external": {
        "label": "修改文件",
        "short_name": "修改（所有）",
        "description": "允许修改计算机上的任何文件。"
      },
      "execute_commands": {
        "label": "执行安全命令",
        "short_name": "命令",
        "description": "允许执行安全终端命令。如果模型确定某个命令具有潜在的破坏性，则仍需要批准。"
      },
      "execute_all_commands": {
        "label": "执行所有命令",
        "short_name": "所有命令",
        "description": "允许执行所有终端命令，使用风险自负。"
      },
      "use_browser": {
        "label": "使用浏览器",
        "short_name": "浏览",
        "description": "允许在浏览器中启动任何网站并与之交互。"
      },
      "context_menu": {
        "no_results_found": "没有找到结果",
        "problems": "问题",
        "terminal": "命令",
        "git_commits": "Git提交记录",
        "searching": "正在搜索...",
        "add": "添加",
        "file": "文件",
        "folder": "文件夹",
        "paste_url_to_fetch_contents": "粘贴URL以获取内容"
      },
      "use_mcp": {
        "label": "使用MCP服务器",
        "short_name": "MCP",
        "description": "允许使用已配置的MCP服务器，这些服务器可以修改文件系统或与API交互。"
      },
      "max_requests": "最大请求数：",
      "max_requests_description": "零号员工将自动提出这么多API请求，然后再请求批准以继续执行任务。",
      "enable_notification": "启用通知",
      "enable_notification_description": "当零号员工需要批准才能继续或任务完成时，接收系统通知。"
    },
    "bottom_bar": {
      "add_context": "添加上下文",
      "add_image": "添加图片",
      "manage_mcp_servers": "管理MCP服务器",
      "mcp_servers": "MCP服务器",
      "no_mcp_installed": "MCP服务器未安装",
      "manage_rules": "管理零号员工规则",
      "rules_title": "零号员工规则",
      "global_rules": "全局规则",
      "workspace_rules": "工作区规则",
      "no_rules_found": "未找到规则",
      "new_rule_file": "新规则文件",
      "new_rule_file_Ellipses": "新规则文件...",
      "rule_name_format": "规则名称（.md、.txt或无扩展名)",
      "select_model": "选择模型/API提供商",
      "plan": "计划",
      "plan_tooltip": "在计划模式下，零号员工将收集信息以制定计划",
      "act": "执行",
      "act_tooltip": "在执行模式下，零号员工将立即完成任务",
      "toggle_mode_hint": "使用w/ {{0}}+Shift+A切换"
    },
    "retry": "重试",
    "new_task": "新建任务",
    "new_ctx_task": "新建上下文任务",
    "proceed_anyways": "继续执行",
    "proceed": "继续",
    "save": "保存",
    "reject": "拒绝",
    "approve": "批准",
    "run_command": "执行命令",
    "resume_task": "恢复任务",
    "proceed_while_running": "运行时继续",
    "cancel": "取消",
    "want_read_file": "读取此文件：",
    "read_file": "已读取该文件:",
    "want_create_file": "创建一个新文件：",
    "create_file": "已创建一个新文件:",
    "want_edit_file": "编辑此文件：",
    "edit_file": "已编辑该文件:",
    "api_request_cancelled": "已取消API请求",
    "api_streaming_failed": "API流式处理失败",
    "api_request": "API请求",
    "api_requesting": "API请求中...",
    "api_request_failed": "API请求失败",
    "has_a_question": "出现一个问题：",
    "view_top_files": "查看此目录中的顶层文件：",
    "viewed_top_files": "已查看此目录中的顶级文件：",
    "view_all_files": "递归查看此目录中的所有文件：",
    "viewed_all_files": "已递归查看了此目录中的所有文件：",
    "view_source_code": "查看在此目录中使用的源代码定义名称：",
    "viewed_source_code": "已查看在此目录中使用的源代码定义名称：",
    "model_determined_command_requires_approval": "模型已确定此命令需要核对。",
    "use_browser": "使用浏览器：",
    "using_browser": "正在使用浏览器：",
    "execute_command": "执行命令：",
    "shell_integration_unavailable": "命令执行器不可用",
    "executed_command": "命令已执行：",
    "see_new_changes": "查看新更改",
    "not_match": "当模型使用的搜索模式与文件中的任何内容都不匹配时，通常会发生这种情况。正在重试...",
    "diff_edit_failed": "无法编辑差异",
    "still_having_trouble": "还有问题?",
    "task": "任务",
    "see_less": "收起",
    "see_more": "展开",
    "task_complete": "任务完成",
    "context_window": "上下文窗口：",
    "api_cost": "API 价格：",
    "chatIndexingProgress": {
      "indexing": "代码库索引...",
      "done": "代码库索引完成"
    }
  }
}